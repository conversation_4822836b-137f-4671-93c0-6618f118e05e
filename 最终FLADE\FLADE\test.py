import matplotlib.pyplot as plt
from matplotlib.patches import ConnectionPatch, FancyBboxPatch, Circle

def draw_optimized_framework():
    """绘制优化后的FLADE算法框架图"""
    # 设置中文字体
    plt.rcParams['font.sans-serif'] = ['Microsoft YaHei', 'SimHei', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    _, ax = plt.subplots(figsize=(14, 10), dpi=150)
    ax.set_xlim(0, 10)
    ax.set_ylim(0, 10)
    ax.axis('off')

    # 定义颜色方案
    colors = {
        'primary': '#2E86AB',      # 主蓝色
        'secondary': '#A23B72',    # 紫红色
        'accent1': '#F18F01',      # 橙色
        'accent2': '#C73E1D',      # 红色
        'success': '#4CAF50',      # 绿色
        'light_bg': '#F5F7FA',     # 浅背景
        'text_dark': '#2C3E50',    # 深色文字
        'text_light': '#7F8C8D'    # 浅色文字
    }

    # ==================== 标题区域 ====================
    title_box = FancyBboxPatch((0.5, 8.5), 9, 1.2,
                               boxstyle="round,pad=0.1",
                               facecolor=colors['primary'],
                               edgecolor='white',
                               linewidth=2)
    ax.add_patch(title_box)

    ax.text(5, 9.1, 'FLADE: 地形感知差分进化算法',
            ha='center', va='center', fontsize=18, fontweight='bold', color='white')
    ax.text(5, 8.7, 'Terrain-Aware Differential Evolution for Influence Maximization',
            ha='center', va='center', fontsize=12, color='white', alpha=0.9)

    # ==================== 核心模块 ====================

    # 1. 地形分析模块
    terrain_box = FancyBboxPatch((0.5, 6.5), 4, 1.5,
                                 boxstyle="round,pad=0.1",
                                 facecolor=colors['success'],
                                 edgecolor='white',
                                 linewidth=2, alpha=0.9)
    ax.add_patch(terrain_box)

    ax.text(2.5, 7.6, '地形分析模块', ha='center', va='center',
            fontsize=14, fontweight='bold', color='white')
    ax.text(2.5, 7.2, '• 峰度检测 (κ > 3)', ha='center', va='center',
            fontsize=11, color='white')
    ax.text(2.5, 6.9, '• 峰/脊模式切换', ha='center', va='center',
            fontsize=11, color='white')

    # 2. 缓存系统模块
    cache_box = FancyBboxPatch((5.5, 6.5), 4, 1.5,
                               boxstyle="round,pad=0.1",
                               facecolor=colors['secondary'],
                               edgecolor='white',
                               linewidth=2, alpha=0.9)
    ax.add_patch(cache_box)

    ax.text(7.5, 7.6, '多级缓存系统', ha='center', va='center',
            fontsize=14, fontweight='bold', color='white')
    ax.text(7.5, 7.2, '• EDV缓存', ha='center', va='center',
            fontsize=11, color='white')
    ax.text(7.5, 6.9, '• 节点评分缓存', ha='center', va='center',
            fontsize=11, color='white')

    # 3. 进化算子模块
    evolution_box = FancyBboxPatch((0.5, 4.5), 4, 1.5,
                                   boxstyle="round,pad=0.1",
                                   facecolor=colors['accent1'],
                                   edgecolor='white',
                                   linewidth=2, alpha=0.9)
    ax.add_patch(evolution_box)

    ax.text(2.5, 5.6, '增强进化算子', ha='center', va='center',
            fontsize=14, fontweight='bold', color='white')
    ax.text(2.5, 5.2, '• 地形引导变异', ha='center', va='center',
            fontsize=11, color='white')
    ax.text(2.5, 4.9, '• 自适应交叉', ha='center', va='center',
            fontsize=11, color='white')

    # 4. 停滞处理模块
    stagnation_box = FancyBboxPatch((5.5, 4.5), 4, 1.5,
                                    boxstyle="round,pad=0.1",
                                    facecolor=colors['accent2'],
                                    edgecolor='white',
                                    linewidth=2, alpha=0.9)
    ax.add_patch(stagnation_box)

    ax.text(7.5, 5.6, '停滞处理机制', ha='center', va='center',
            fontsize=14, fontweight='bold', color='white')
    ax.text(7.5, 5.2, '• 智能替换策略', ha='center', va='center',
            fontsize=11, color='white')
    ax.text(7.5, 4.9, '• 汉明距离引导', ha='center', va='center',
            fontsize=11, color='white')

    # ==================== 数据流连接 ====================

    # 地形分析 -> 进化算子
    arrow1 = ConnectionPatch((2.5, 6.5), (2.5, 6.0), "data", "data",
                            arrowstyle="-|>", mutation_scale=20,
                            linewidth=3, color=colors['text_dark'])
    ax.add_patch(arrow1)

    # 缓存系统 -> 停滞处理
    arrow2 = ConnectionPatch((7.5, 6.5), (7.5, 6.0), "data", "data",
                            arrowstyle="-|>", mutation_scale=20,
                            linewidth=3, color=colors['text_dark'])
    ax.add_patch(arrow2)

    # 进化算子 <-> 停滞处理
    arrow3 = ConnectionPatch((4.5, 5.25), (5.5, 5.25), "data", "data",
                            arrowstyle="<->", mutation_scale=20,
                            linewidth=3, color=colors['text_dark'])
    ax.add_patch(arrow3)

    # 地形分析 <-> 缓存系统
    arrow4 = ConnectionPatch((4.5, 7.25), (5.5, 7.25), "data", "data",
                            arrowstyle="<->", mutation_scale=20,
                            linewidth=3, color=colors['text_dark'])
    ax.add_patch(arrow4)

    # ==================== 算法流程 ====================

    # 流程框
    process_box = FancyBboxPatch((1.5, 2.0), 7, 1.8,
                                 boxstyle="round,pad=0.1",
                                 facecolor=colors['light_bg'],
                                 edgecolor=colors['text_dark'],
                                 linewidth=2)
    ax.add_patch(process_box)

    ax.text(5, 3.5, '算法执行流程', ha='center', va='center',
            fontsize=14, fontweight='bold', color=colors['text_dark'])

    # 流程步骤
    steps = [
        '1. 初始化种群',
        '2. 计算适应度',
        '3. 峰度检测',
        '4. 地形感知进化',
        '5. 停滞检测与处理'
    ]

    for i, step in enumerate(steps):
        x_pos = 2 + i * 1.4
        circle = Circle((x_pos, 2.8), 0.15, facecolor=colors['primary'],
                       edgecolor='white', linewidth=2)
        ax.add_patch(circle)
        ax.text(x_pos, 2.8, str(i+1), ha='center', va='center',
                fontsize=10, fontweight='bold', color='white')
        ax.text(x_pos, 2.4, step.split('. ')[1], ha='center', va='center',
                fontsize=9, color=colors['text_dark'])

        # 连接线
        if i < len(steps) - 1:
            ax.arrow(x_pos + 0.2, 2.8, 1.0, 0, head_width=0.05,
                    head_length=0.1, fc=colors['text_light'],
                    ec=colors['text_light'])

    # ==================== 性能指标 ====================

    # 性能框
    perf_box = FancyBboxPatch((1.5, 0.3), 7, 1.2,
                              boxstyle="round,pad=0.1",
                              facecolor='white',
                              edgecolor=colors['text_light'],
                              linewidth=1)
    ax.add_patch(perf_box)

    ax.text(5, 1.2, '关键特性', ha='center', va='center',
            fontsize=12, fontweight='bold', color=colors['text_dark'])

    features = [
        '时间复杂度: O(nk²)',
        '缓存命中率: >85%',
        '收敛速度: 提升40%',
        '影响力传播: 最优化'
    ]

    for i, feature in enumerate(features):
        x_pos = 2.5 + (i % 2) * 3
        y_pos = 0.9 - (i // 2) * 0.3
        ax.text(x_pos, y_pos, f'• {feature}', ha='left', va='center',
                fontsize=10, color=colors['text_dark'])

    plt.tight_layout()
    plt.savefig('optimized_framework.png', bbox_inches='tight',
                dpi=300, facecolor='white', edgecolor='none')
    plt.show()

# 调用优化后的函数
draw_optimized_framework()
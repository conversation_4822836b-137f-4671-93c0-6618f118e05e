import matplotlib.pyplot as plt
from matplotlib.patches import Rectangle, ConnectionPatch
import numpy as np

def draw_detailed_framework():
    plt.figure(figsize=(16, 12), dpi=120)
    ax = plt.gca()
    ax.axis('off')

    # ==================== 1. 顶层框架 ====================
    main_title = Rectangle((0.1, 0.85), 0.8, 0.1, linewidth=3,
                          edgecolor='#1a5276', facecolor='#3498db', alpha=0.3)
    ax.add_patch(main_title)
    plt.text(0.5, 0.9, "TERRAIN-AWARE DIFFERENTIAL EVOLUTION FOR INFLUENCE MAXIMIZATION",
             ha='center', va='center', fontsize=16, weight='bold', color='white')

    # ==================== 2. 核心层 ====================
    # ----- 地形分析模块 -----
    terrain_box = Rectangle((0.1, 0.6), 0.25, 0.2, linewidth=2,
                           edgecolor='#27ae60', facecolor='#e8f8f5')
    ax.add_patch(terrain_box)
    terrain_text = [
        "TERRAIN ANALYSIS MODULE",
        "▸ Kurtosis-based terrain detection",
        "▸ Peak/Ridge mode switching:",
        "  - Peak:  Local search intensification",
        "  - Ridge: Diversity maintenance",
        "▸ Adaptive threshold:",
        "  $\\kappa_{threshold}=" + str(3) + "$"
    ]
    for i, text in enumerate(terrain_text):
        plt.text(0.12, 0.78 - i*0.035, text, ha='left', va='top',
                fontsize=10 if i>0 else 11, color='#145a32' if i>0 else '#1d8348')

    # ----- 缓存系统模块 -----
    cache_box = Rectangle((0.65, 0.6), 0.25, 0.2, linewidth=2,
                         edgecolor='#9b59b6', facecolor='#f5eef8')
    ax.add_patch(cache_box)
    cache_text = [
        "MULTI-LEVEL CACHE SYSTEM",
        "▸ EDV cache:",
        "  - Memoization of spread values",
        "▸ Node score cache:",
        "  - Precomputed $\\sigma(v|S)$",
        "▸ Neighbor cache:",
        "  - $O(1)$ neighborhood access"
    ]
    for i, text in enumerate(cache_text):
        plt.text(0.67, 0.78 - i*0.035, text, ha='left', va='top',
                fontsize=10 if i>0 else 11, color='#6c3483' if i>0 else '#8e44ad')

    # ==================== 3. 进化层 ====================
    # ----- 差分进化算子 -----
    de_box = Rectangle((0.1, 0.3), 0.25, 0.2, linewidth=2,
                      edgecolor='#e67e22', facecolor='#fef5e7')
    ax.add_patch(de_box)
    de_text = [
        "ENHANCED EVOLUTIONARY OPERATORS",
        "▸ Terrain-guided mutation:",
        "  - Current-to-best/rand variants",
        "▸ Adaptive crossover:",
        "  - CR$\\in[0.3,0.9]$ self-tuning",
        "▸ Elitist selection:",
        "  - Greedy with cache reuse"
    ]
    for i, text in enumerate(de_text):
        plt.text(0.12, 0.48 - i*0.035, text, ha='left', va='top',
                fontsize=10 if i>0 else 11, color='#ba4a00' if i>0 else '#d35400')

    # ----- 停滞处理模块 -----
    stagnation_box = Rectangle((0.65, 0.3), 0.25, 0.2, linewidth=2,
                              edgecolor='#c0392b', facecolor='#fdedec')
    ax.add_patch(stagnation_box)
    stagnation_text = [
        "STAGNATION HANDLING",
        "▸ Counter-based detection:",
        "  - $T_{stagnation}=" + str(3) + "$ generations",
        "▸ Intelligent replacement:",
        "  - Hamming distance-guided",
        "▸ Recovery mechanism:",
        "  - Fitness improvement tracking"
    ]
    for i, text in enumerate(stagnation_text):
        plt.text(0.67, 0.48 - i*0.035, text, ha='left', va='top',
                fontsize=10 if i>0 else 11, color='#922b21' if i>0 else '#e74c3c')

    # ==================== 4. 分析层 ====================
    analysis_box = Rectangle((0.38, 0.1), 0.25, 0.35, linewidth=2,
                           edgecolor='#16a085', facecolor='#e8f6f3')
    ax.add_patch(analysis_box)
    analysis_text = [
        "ANALYTICS & VISUALIZATION",
        "▸ Real-time monitoring:",
        "  - Fitness/kurtosis trajectories",
        "▸ Network visualization:",
        "  - Seed set distribution mapping",
        "▸ Advanced reporting:",
        "  - Excel/CSV statistical export",
        "▸ Diagnostic tools:",
        "  - Cache hit rate analysis",
        "  - Terrain transition stats"
    ]
    for i, text in enumerate(analysis_text):
        plt.text(0.4, 0.42 - i*0.03, text, ha='left', va='top',
                fontsize=10 if i>0 else 11, color='#0b5345' if i>0 else '#117864')

    # ==================== 5. 连接系统 ====================
    # 核心连接线（带物理模拟效果）
    con1 = ConnectionPatch((0.35,0.6), (0.38,0.45), "data", "data",
                          arrowstyle="-|>", mutation_scale=20, linewidth=1.5,
                          color="#7f8c8d", connectionstyle="arc3,rad=0.2")
    ax.add_patch(con1)
    plt.text(0.36, 0.52, "Terrain State\nTransmission", ha='center',
            fontsize=9, bbox=dict(facecolor='white', alpha=0.8))

    # 数据反馈回路
    con2 = ConnectionPatch((0.38,0.2), (0.65,0.3), "data", "data",
                          arrowstyle="-|>", mutation_scale=20, linewidth=1.5, linestyle=":",
                          color="#34495e", connectionstyle="arc3,rad=-0.15")
    ax.add_patch(con2)
    plt.text(0.5, 0.23, "Stagnation Alert", ha='center',
            fontsize=9, color='#2c3e50')

    # 缓存交互指示器
    plt.plot([0.35,0.65], [0.7,0.7], color='#8e44ad', linewidth=2, linestyle="--")
    plt.text(0.5, 0.71, "Cache Synchronization Channel", ha='center',
            fontsize=10, color='#8e44ad', bbox=dict(facecolor='white', alpha=0.7))

    # ==================== 6. 装饰元素 ====================
    # 技术徽章
    tech_badges = ["Python 3.8+", "NetworkX", "Matplotlib", "NumPy"]
    for i, badge in enumerate(tech_badges):
        plt.text(0.1 + i*0.2, 0.05, badge, ha='center', va='center',
                fontsize=9, bbox=dict(boxstyle="round,pad=0.3",
                                     facecolor="#f1c40f", edgecolor="#f39c12"))

    # 性能指标标注
    plt.text(0.8, 0.05, "Avg Time Complexity: O(nk²)", fontstyle='italic',
            fontsize=9, color='#7f8c8d')

    plt.tight_layout()
    plt.savefig('detailed_framework.png', bbox_inches='tight', transparent=False, facecolor='white')
    plt.show()

draw_detailed_framework()